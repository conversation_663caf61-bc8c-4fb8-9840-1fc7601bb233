{"IDE_Data": {"property_items": [{"proitem_defuival": "", "proitem_defvalue": "VHJ1ZQ==", "proitem_parcount": "MQ==", "proitem_parname": "bd_automatch"}, {"proitem_defuival": "", "proitem_defvalue": "VHJ1ZQ==", "proitem_parcount": "MQ==", "proitem_parname": "bd_closeime"}, {"proitem_defuival": "", "proitem_defvalue": "VHJ1ZQ==", "proitem_parcount": "MQ==", "proitem_parname": "bd_componentlog"}, {"proitem_defuival": "", "proitem_defvalue": "MA==", "proitem_parcount": "MQ==", "proitem_parname": "bd_err_manage"}, {"proitem_defuival": "", "proitem_defvalue": "MA==", "proitem_parcount": "MQ==", "proitem_parname": "bd_errtocache"}, {"proitem_defuival": "", "proitem_defvalue": "RmFsc2U=", "proitem_parcount": "MQ==", "proitem_parname": "bd_isadmin"}, {"proitem_defuival": "", "proitem_defvalue": "RmFsc2U=", "proitem_parcount": "MQ==", "proitem_parname": "bd_isconcurrent"}, {"proitem_defuival": "", "proitem_defvalue": "VHJ1ZQ==", "proitem_parcount": "MQ==", "proitem_parname": "bd_isdbginfo"}, {"proitem_defuival": "", "proitem_defvalue": "RmFsc2U=", "proitem_parcount": "MQ==", "proitem_parname": "bd_iserrcutscr"}, {"proitem_defuival": "", "proitem_defvalue": "VHJ1ZQ==", "proitem_parcount": "MQ==", "proitem_parname": "bd_isnoteprint"}, {"proitem_defuival": "", "proitem_defvalue": "Mw==", "proitem_parcount": "MQ==", "proitem_parname": "bd_loglevel"}, {"proitem_defuival": "", "proitem_defvalue": "5Y+v6KeG5YyWSURF6aG555uu77yB", "proitem_parcount": "MQ==", "proitem_parname": "bd_project_desc"}, {"proitem_defuival": "", "proitem_defvalue": "TFNJREU=", "proitem_parcount": "MQ==", "proitem_parname": "bd_project_developer"}, {"proitem_defuival": "", "proitem_defvalue": "MA==", "proitem_parcount": "MQ==", "proitem_parname": "bd_project_icon"}, {"proitem_defuival": "", "proitem_defvalue": "6aG555uuMQ==", "proitem_parcount": "MQ==", "proitem_parname": "bd_project_name"}, {"proitem_defuival": "", "proitem_defvalue": "", "proitem_parcount": "MQ==", "proitem_parname": "bd_project_passwd"}, {"proitem_defuival": "", "proitem_defvalue": "MS4wLjAuMA==", "proitem_parcount": "MQ==", "proitem_parname": "bd_project_version"}, {"proitem_defuival": "", "proitem_defvalue": "MA==", "proitem_parcount": "MQ==", "proitem_parname": "bd_w3d_interval"}, {"proitem_defuival": "", "proitem_defvalue": "RmFsc2U=", "proitem_parcount": "MQ==", "proitem_parname": "bd_w3d_isrecord"}]}, "IDE_Flows": [{"Flows": [{"file_path": "需求说明.docx"}, {"file_path": "需求列表.xlsx"}], "Group": "需求文档"}], "IDE_Info": {"IDE_EVN": "x86", "Project_ChangeTime": "2025-08-07 15:16:02", "Project_Component": [], "Project_CreateTime": "2025-08-07 15:16:02", "Project_DevNo": "4F486537A230ED9D688F8835F6681A32", "Project_EntId": "1100000000", "Project_GUID": "3ECDE690AA97A06A1409D8D22C946BEC", "Project_IDEVer": "7.8.6.2", "Project_IDEVerMain": "7.8.6.2", "Project_Name": "项目1", "Project_OSType": "win", "Project_PTag": "pro_2508071516020082", "Project_PType": "Professional", "Project_UserId": "", "Project_UserName": ""}}